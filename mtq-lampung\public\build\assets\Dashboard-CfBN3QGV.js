import{_ as x}from"./AppLayout.vue_vue_type_script_setup_true_lang-CsS4_fpT.js";import{_ as m}from"./Heading.vue_vue_type_script_setup_true_lang-BUNpQE6u.js";import{_ as n}from"./Icon.vue_vue_type_script_setup_true_lang-ByEZOZXd.js";import{d as _,c as u,o as r,w as d,a as t,b as i,t as a,e as l,F as f,r as h,n as y,f as b}from"./app-CodSZJda.js";import"./AppLogoIcon.vue_vue_type_script_setup_true_lang-Dcme7roQ.js";import"./useForwardExpose-pMOtue68.js";import"./RovingFocusGroup-BPhEOCRK.js";import"./check-DU8Q6j8i.js";import"./loader-circle-DchkMPCL.js";import"./sun-C512EHbT.js";const w={class:"space-y-6"},v={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},k={class:"bg-white rounded-lg shadow p-6"},D={class:"flex items-center"},T={class:"flex-shrink-0"},C={class:"ml-4"},P={class:"text-2xl font-semibold text-gray-900"},S={class:"bg-white rounded-lg shadow p-6"},j={class:"flex items-center"},B={class:"flex-shrink-0"},V={class:"ml-4"},L={class:"text-2xl font-semibold text-gray-900"},N={class:"bg-white rounded-lg shadow p-6"},$={class:"flex items-center"},F={class:"flex-shrink-0"},M={class:"ml-4"},z={class:"text-2xl font-semibold text-gray-900"},A={class:"bg-white rounded-lg shadow p-6"},E={class:"flex items-center"},G={class:"flex-shrink-0"},I={class:"ml-4"},q={class:"text-2xl font-semibold text-gray-900"},H={class:"bg-white rounded-lg shadow"},J={class:"overflow-x-auto"},K={class:"min-w-full divide-y divide-gray-200"},O={class:"bg-white divide-y divide-gray-200"},Q={class:"px-6 py-4 whitespace-nowrap"},R={class:"text-sm font-medium text-gray-900"},U={class:"text-sm text-gray-500"},W={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},X={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Y={class:"px-6 py-4 whitespace-nowrap"},Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},pt=_({__name:"Dashboard",props:{stats:{},recent_pendaftaran:{}},setup(tt){function c(s){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",payment_pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",verified:"bg-indigo-100 text-indigo-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"}[s]||"bg-gray-100 text-gray-800"}function p(s){return{draft:"Draft",submitted:"Disubmit",payment_pending:"Menunggu Pembayaran",paid:"Dibayar",verified:"Diverifikasi",approved:"Disetujui",rejected:"Ditolak"}[s]||s}function g(s){return new Date(s).toLocaleDateString("id-ID",{year:"numeric",month:"short",day:"numeric"})}return(s,e)=>(r(),u(x,null,{header:d(()=>[i(m,null,{default:d(()=>e[0]||(e[0]=[b("Dashboard Admin")])),_:1,__:[0]})]),default:d(()=>[t("div",w,[t("div",v,[t("div",k,[t("div",D,[t("div",T,[i(n,{name:"users",class:"h-8 w-8 text-blue-600"})]),t("div",C,[e[1]||(e[1]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Peserta",-1)),t("p",P,a(s.stats.total_peserta),1)])])]),t("div",S,[t("div",j,[t("div",B,[i(n,{name:"file-text",class:"h-8 w-8 text-green-600"})]),t("div",V,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-500"},"Total Pendaftaran",-1)),t("p",L,a(s.stats.total_pendaftaran),1)])])]),t("div",N,[t("div",$,[t("div",F,[i(n,{name:"clock",class:"h-8 w-8 text-yellow-600"})]),t("div",M,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500"},"Menunggu Verifikasi",-1)),t("p",z,a(s.stats.pendaftaran_pending),1)])])]),t("div",A,[t("div",E,[t("div",G,[i(n,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),t("div",I,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500"},"Disetujui",-1)),t("p",q,a(s.stats.pendaftaran_approved),1)])])])]),t("div",H,[e[6]||(e[6]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Pendaftaran Terbaru")],-1)),t("div",J,[t("table",K,[e[5]||(e[5]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Peserta "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Cabang Lomba "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Golongan "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tanggal ")])],-1)),t("tbody",O,[(r(!0),l(f,null,h(s.recent_pendaftaran,o=>(r(),l("tr",{key:o.id_pendaftaran},[t("td",Q,[t("div",R,a(o.peserta.nama_lengkap),1),t("div",U,a(o.nomor_pendaftaran),1)]),t("td",W,a(o.golongan.cabang_lomba.nama_cabang),1),t("td",X,a(o.golongan.nama_golongan),1),t("td",Y,[t("span",{class:y([c(o.status_pendaftaran),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(p(o.status_pendaftaran)),3)]),t("td",Z,a(g(o.created_at)),1)]))),128))])])])])])]),_:1}))}});export{pt as default};
