import{c as e}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-Dcme7roQ.js";import{d as t,e as o,o as n,k as c,n as d,u as l}from"./app-CodSZJda.js";const u=t({__name:"CardHeader",props:{class:{}},setup(a){const s=a;return(r,i)=>(n(),o("div",{"data-slot":"card-header",class:d(l(e)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s.class))},[c(r.$slots,"default")],2))}}),_=t({__name:"CardTitle",props:{class:{}},setup(a){const s=a;return(r,i)=>(n(),o("h3",{"data-slot":"card-title",class:d(l(e)("leading-none font-semibold",s.class))},[c(r.$slots,"default")],2))}});export{u as _,_ as a};
