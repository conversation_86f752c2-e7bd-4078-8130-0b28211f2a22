import{d as x,x as b,c as f,o as u,w as n,a as s,b as r,f as i,g as q,u as l,h as _,v as k,i as j,e as y,F as M,r as U,t as V,j as $}from"./app-CodSZJda.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-CsS4_fpT.js";import{_ as N}from"./Heading.vue_vue_type_script_setup_true_lang-BUNpQE6u.js";import{_ as g}from"./AppLogoIcon.vue_vue_type_script_setup_true_lang-Dcme7roQ.js";import{_ as P,a as h}from"./CardContent.vue_vue_type_script_setup_true_lang-CPR0xiPo.js";import{_ as K,a as L}from"./CardTitle.vue_vue_type_script_setup_true_lang-CmjrBhaC.js";import{_ as T}from"./CardDescription.vue_vue_type_script_setup_true_lang-DNRVAQBn.js";import{_ as m}from"./Input.vue_vue_type_script_setup_true_lang-cX9t23Lk.js";import{_ as t}from"./Label.vue_vue_type_script_setup_true_lang-B0iw1qR4.js";import{_ as d}from"./InputError.vue_vue_type_script_setup_true_lang-Cbzd25wk.js";import{_ as v}from"./Icon.vue_vue_type_script_setup_true_lang-ByEZOZXd.js";import"./useForwardExpose-pMOtue68.js";import"./RovingFocusGroup-BPhEOCRK.js";import"./check-DU8Q6j8i.js";import"./loader-circle-DchkMPCL.js";import"./sun-C512EHbT.js";const B={class:"flex items-center space-x-4"},S={class:"max-w-4xl mx-auto"},A={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},C={class:"space-y-4"},D={class:"space-y-4"},E={class:"grid grid-cols-2 gap-4"},F={class:"space-y-4"},z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},J=["value"],W={class:"space-y-4"},G={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},H={class:"space-y-4"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Q={class:"flex justify-end space-x-4 pt-6 border-t"},pa=x({__name:"Create",props:{wilayah:{}},setup(R){const e=b({username:"",email:"",password:"",nik:"",nama_lengkap:"",tempat_lahir:"",tanggal_lahir:"",jenis_kelamin:"",alamat:"",id_wilayah:"",no_telepon:"",nama_ayah:"",nama_ibu:"",pekerjaan:"",instansi_asal:""});function w(){e.post(route("admin.peserta.store"),{onSuccess:()=>{}})}return(p,a)=>(u(),f(I,null,{header:n(()=>[s("div",B,[r(g,{as:"link",href:p.route("admin.peserta.index"),variant:"ghost",size:"sm"},{default:n(()=>[r(v,{name:"arrow-left",class:"w-4 h-4 mr-2"}),a[15]||(a[15]=i(" Kembali "))]),_:1,__:[15]},8,["href"]),r(N,null,{default:n(()=>a[16]||(a[16]=[i("Tambah Peserta Baru")])),_:1,__:[16]})])]),default:n(()=>[s("div",S,[r(P,null,{default:n(()=>[r(K,null,{default:n(()=>[r(L,null,{default:n(()=>a[17]||(a[17]=[i("Informasi Peserta")])),_:1,__:[17]}),r(T,null,{default:n(()=>a[18]||(a[18]=[i(" Lengkapi form di bawah ini untuk menambahkan peserta baru ")])),_:1,__:[18]})]),_:1}),r(h,null,{default:n(()=>[s("form",{onSubmit:q(w,["prevent"]),class:"space-y-6"},[s("div",A,[s("div",C,[a[23]||(a[23]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Akun",-1)),s("div",null,[r(t,{for:"username",required:""},{default:n(()=>a[19]||(a[19]=[i("Username")])),_:1,__:[19]}),r(m,{id:"username",modelValue:l(e).username,"onUpdate:modelValue":a[0]||(a[0]=o=>l(e).username=o),type:"text",error:l(e).errors.username,placeholder:"Masukkan username",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.username},null,8,["message"])]),s("div",null,[r(t,{for:"email",required:""},{default:n(()=>a[20]||(a[20]=[i("Email")])),_:1,__:[20]}),r(m,{id:"email",modelValue:l(e).email,"onUpdate:modelValue":a[1]||(a[1]=o=>l(e).email=o),type:"email",error:l(e).errors.email,placeholder:"Masukkan email",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.email},null,8,["message"])]),s("div",null,[r(t,{for:"password",required:""},{default:n(()=>a[21]||(a[21]=[i("Password")])),_:1,__:[21]}),r(m,{id:"password",modelValue:l(e).password,"onUpdate:modelValue":a[2]||(a[2]=o=>l(e).password=o),type:"password",error:l(e).errors.password,placeholder:"Masukkan password",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.password},null,8,["message"]),a[22]||(a[22]=s("p",{class:"mt-1 text-sm text-gray-500"},"Minimal 8 karakter",-1))])]),s("div",D,[a[30]||(a[30]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Pribadi",-1)),s("div",null,[r(t,{for:"nik",required:""},{default:n(()=>a[24]||(a[24]=[i("NIK")])),_:1,__:[24]}),r(m,{id:"nik",modelValue:l(e).nik,"onUpdate:modelValue":a[3]||(a[3]=o=>l(e).nik=o),type:"text",error:l(e).errors.nik,placeholder:"Masukkan NIK (16 digit)",maxlength:"16",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.nik},null,8,["message"])]),s("div",null,[r(t,{for:"nama_lengkap",required:""},{default:n(()=>a[25]||(a[25]=[i("Nama Lengkap")])),_:1,__:[25]}),r(m,{id:"nama_lengkap",modelValue:l(e).nama_lengkap,"onUpdate:modelValue":a[4]||(a[4]=o=>l(e).nama_lengkap=o),type:"text",error:l(e).errors.nama_lengkap,placeholder:"Masukkan nama lengkap",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.nama_lengkap},null,8,["message"])]),s("div",E,[s("div",null,[r(t,{for:"tempat_lahir",required:""},{default:n(()=>a[26]||(a[26]=[i("Tempat Lahir")])),_:1,__:[26]}),r(m,{id:"tempat_lahir",modelValue:l(e).tempat_lahir,"onUpdate:modelValue":a[5]||(a[5]=o=>l(e).tempat_lahir=o),type:"text",error:l(e).errors.tempat_lahir,placeholder:"Kota lahir",class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.tempat_lahir},null,8,["message"])]),s("div",null,[r(t,{for:"tanggal_lahir",required:""},{default:n(()=>a[27]||(a[27]=[i("Tanggal Lahir")])),_:1,__:[27]}),r(m,{id:"tanggal_lahir",modelValue:l(e).tanggal_lahir,"onUpdate:modelValue":a[6]||(a[6]=o=>l(e).tanggal_lahir=o),type:"date",error:l(e).errors.tanggal_lahir,class:"mt-1",required:""},null,8,["modelValue","error"]),r(d,{message:l(e).errors.tanggal_lahir},null,8,["message"])])]),s("div",null,[r(t,{for:"jenis_kelamin",required:""},{default:n(()=>a[28]||(a[28]=[i("Jenis Kelamin")])),_:1,__:[28]}),_(s("select",{id:"jenis_kelamin","onUpdate:modelValue":a[7]||(a[7]=o=>l(e).jenis_kelamin=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},a[29]||(a[29]=[s("option",{value:""},"Pilih jenis kelamin",-1),s("option",{value:"L"},"Laki-laki",-1),s("option",{value:"P"},"Perempuan",-1)]),512),[[k,l(e).jenis_kelamin]]),r(d,{message:l(e).errors.jenis_kelamin},null,8,["message"])])])]),s("div",F,[a[35]||(a[35]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Kontak",-1)),s("div",null,[r(t,{for:"alamat",required:""},{default:n(()=>a[31]||(a[31]=[i("Alamat")])),_:1,__:[31]}),_(s("textarea",{id:"alamat","onUpdate:modelValue":a[8]||(a[8]=o=>l(e).alamat=o),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Masukkan alamat lengkap",required:""},null,512),[[j,l(e).alamat]]),r(d,{message:l(e).errors.alamat},null,8,["message"])]),s("div",z,[s("div",null,[r(t,{for:"id_wilayah",required:""},{default:n(()=>a[32]||(a[32]=[i("Wilayah")])),_:1,__:[32]}),_(s("select",{id:"id_wilayah","onUpdate:modelValue":a[9]||(a[9]=o=>l(e).id_wilayah=o),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[a[33]||(a[33]=s("option",{value:""},"Pilih wilayah",-1)),(u(!0),y(M,null,U(p.wilayah,o=>(u(),y("option",{key:o.id_wilayah,value:o.id_wilayah},V(o.nama_wilayah),9,J))),128))],512),[[k,l(e).id_wilayah]]),r(d,{message:l(e).errors.id_wilayah},null,8,["message"])]),s("div",null,[r(t,{for:"no_telepon"},{default:n(()=>a[34]||(a[34]=[i("No. Telepon")])),_:1,__:[34]}),r(m,{id:"no_telepon",modelValue:l(e).no_telepon,"onUpdate:modelValue":a[10]||(a[10]=o=>l(e).no_telepon=o),type:"tel",error:l(e).errors.no_telepon,placeholder:"Masukkan nomor telepon",class:"mt-1"},null,8,["modelValue","error"]),r(d,{message:l(e).errors.no_telepon},null,8,["message"])])])]),s("div",W,[a[38]||(a[38]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Keluarga",-1)),s("div",G,[s("div",null,[r(t,{for:"nama_ayah"},{default:n(()=>a[36]||(a[36]=[i("Nama Ayah")])),_:1,__:[36]}),r(m,{id:"nama_ayah",modelValue:l(e).nama_ayah,"onUpdate:modelValue":a[11]||(a[11]=o=>l(e).nama_ayah=o),type:"text",error:l(e).errors.nama_ayah,placeholder:"Masukkan nama ayah",class:"mt-1"},null,8,["modelValue","error"]),r(d,{message:l(e).errors.nama_ayah},null,8,["message"])]),s("div",null,[r(t,{for:"nama_ibu"},{default:n(()=>a[37]||(a[37]=[i("Nama Ibu")])),_:1,__:[37]}),r(m,{id:"nama_ibu",modelValue:l(e).nama_ibu,"onUpdate:modelValue":a[12]||(a[12]=o=>l(e).nama_ibu=o),type:"text",error:l(e).errors.nama_ibu,placeholder:"Masukkan nama ibu",class:"mt-1"},null,8,["modelValue","error"]),r(d,{message:l(e).errors.nama_ibu},null,8,["message"])])])]),s("div",H,[a[41]||(a[41]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informasi Tambahan",-1)),s("div",O,[s("div",null,[r(t,{for:"pekerjaan"},{default:n(()=>a[39]||(a[39]=[i("Pekerjaan")])),_:1,__:[39]}),r(m,{id:"pekerjaan",modelValue:l(e).pekerjaan,"onUpdate:modelValue":a[13]||(a[13]=o=>l(e).pekerjaan=o),type:"text",error:l(e).errors.pekerjaan,placeholder:"Masukkan pekerjaan",class:"mt-1"},null,8,["modelValue","error"]),r(d,{message:l(e).errors.pekerjaan},null,8,["message"])]),s("div",null,[r(t,{for:"instansi_asal"},{default:n(()=>a[40]||(a[40]=[i("Instansi Asal")])),_:1,__:[40]}),r(m,{id:"instansi_asal",modelValue:l(e).instansi_asal,"onUpdate:modelValue":a[14]||(a[14]=o=>l(e).instansi_asal=o),type:"text",error:l(e).errors.instansi_asal,placeholder:"Masukkan instansi asal",class:"mt-1"},null,8,["modelValue","error"]),r(d,{message:l(e).errors.instansi_asal},null,8,["message"])])])]),s("div",Q,[r(g,{as:"link",href:p.route("admin.peserta.index"),variant:"outline"},{default:n(()=>a[42]||(a[42]=[i(" Batal ")])),_:1,__:[42]},8,["href"]),r(g,{type:"submit",variant:"primary",disabled:l(e).processing},{default:n(()=>[l(e).processing?(u(),f(v,{key:0,name:"loader-2",class:"w-4 h-4 mr-2 animate-spin"})):$("",!0),i(" "+V(l(e).processing?"Menyimpan...":"Simpan Peserta"),1)]),_:1},8,["disabled"])])],32)]),_:1})]),_:1})])]),_:1}))}});export{pa as default};
